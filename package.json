{"name": "camera-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.8.7", "pinia": "^2.2.6", "sass": "^1.80.6", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "typescript": "~5.6.2", "unplugin-auto-import": "^0.18.3", "unplugin-icons": "^0.20.1", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.10", "vite-plugin-vue-devtools": "^7.6.3", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.1.8"}}